import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/layout/bento-grid";
import ProfessionalH<PERSON>Background from "@/components/layout/ProfessionalHeroBackground";
import { <PERSON><PERSON> } from "@/components/ui/button";
import HeroSection from "@/pages/landing/hero-section-page";
import { ArrowR<PERSON>, FileText, Target, TrendingUp } from "lucide-react";
import Link from "next/link";

export default function Home() {
  // console.log("Homepage");
  return (
    <ProfessionalHeroBackground>
      <main className="min-h-screen">
        <HeroSection />
        {/* Sheet Creation Section */}
        <div className="relative py-20">
          <div
            aria-hidden="true"
            className="absolute -top-20 left-1/2 -translate-x-1/2 h-[400px] w-[400px] bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-700 opacity-25 blur-3xl rounded-full -z-10"
          />
          <div className="relative text-center mx-auto max-w-6xl px-4">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Create Personalized Coding Sheets
            </h2>
            <p className="text-lg text-gray-300 mb-12 max-w-3xl mx-auto">
              Transform any Codeforces profile into a structured learning
              experience. Generate custom problem sheets tailored to your skill
              level and track your progress as you master new techniques.
            </p>

            {/* Feature Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-cyan-500/5 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative bg-slate-900/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-blue-500/30 transition-all duration-300">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mb-4 mx-auto">
                    <Target className="w-6 h-6 text-blue-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">
                    Smart Curation
                  </h3>
                  <p className="text-gray-300 text-sm">
                    Automatically organize problems by difficulty rating, tags,
                    and solve status from your Codeforces profile.
                  </p>
                </div>
              </div>

              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/5 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative bg-slate-900/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-green-500/30 transition-all duration-300">
                  <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mb-4 mx-auto">
                    <TrendingUp className="w-6 h-6 text-green-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">
                    Progress Tracking
                  </h3>
                  <p className="text-gray-300 text-sm">
                    Sync problems to your codeforces account and track your
                    progress across different difficulty levels and topics.
                  </p>
                </div>
              </div>

              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/5 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative bg-slate-900/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/30 transition-all duration-300">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-4 mx-auto">
                    <FileText className="w-6 h-6 text-purple-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">
                    <span className="text-lg font-light">Coming soon</span>
                  </h3>
                  <p className="text-gray-300 text-sm">
                    Download your sheets as organized documents or share them
                    with teammates for collaborative practice.
                  </p>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/sheetscope">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white font-semibold px-8 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 group hover:cursor-pointer"
                >
                  Create Your First Sheet
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
        <div className="relative py-12">
          <div
            aria-hidden="true"
            className="absolute inset-0 m-auto h-[357px] w-[357px] rounded-full bg-blue-500/30 opacity-20 blur-3xl"
          />
          <div className="relative text-center mx-auto max-w-5xl px-4">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Features
            </h2>
            <p className="text-lg text-gray-300 mb-10">
              Explore how our tool visualizes your code execution.
            </p>
            <div className="w-full md:w-5/6 mx-auto">
              <BentoDemo />
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="relative py-20">
          <div
            aria-hidden="true"
            className="absolute -top-20 left-1/2 -translate-x-1/2 h-[400px] w-[400px] bg-gradient-to-br from-slate-900 via-slate-800 to-blue-900 opacity-20 blur-3xl rounded-full -z-10"
          />
          <div className="relative text-center mx-auto max-w-6xl px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Meet the Team
            </h2>
            <p className="text-lg text-gray-300 mb-12 max-w-2xl mx-auto">
              The passionate developers behind TraceStack, dedicated to
              enhancing your competitive programming journey.
            </p>

            {/* Team Cards Container */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {/* Person 1 */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-cyan-500/5 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative bg-slate-900/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-8 hover:border-blue-500/30 transition-all duration-300">
                  {/* Avatar placeholder */}
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center mb-6 mx-auto border border-blue-500/30">
                    <span className="text-2xl font-bold text-blue-400">JD</span>
                  </div>

                  <h3 className="text-2xl font-semibold text-white mb-2">
                    John Doe
                  </h3>
                  <p className="text-gray-400 mb-6 text-sm">
                    Full Stack Developer & Competitive Programming Enthusiast
                  </p>

                  {/* Social Links */}
                  <div className="flex justify-center space-x-4">
                    <a
                      href="https://linkedin.com/in/johndoe"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group/link flex items-center space-x-2 px-4 py-2 bg-slate-800/50 hover:bg-blue-600/20 border border-slate-600/50 hover:border-blue-500/50 rounded-lg transition-all duration-300"
                    >
                      <svg
                        className="w-5 h-5 text-blue-400 group-hover/link:text-blue-300"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                      </svg>
                      <span className="text-sm text-gray-300 group-hover/link:text-white">
                        LinkedIn
                      </span>
                    </a>

                    <a
                      href="https://github.com/johndoe"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group/link flex items-center space-x-2 px-4 py-2 bg-slate-800/50 hover:bg-slate-600/20 border border-slate-600/50 hover:border-slate-500/50 rounded-lg transition-all duration-300"
                    >
                      <svg
                        className="w-5 h-5 text-gray-400 group-hover/link:text-gray-300"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                      </svg>
                      <span className="text-sm text-gray-300 group-hover/link:text-white">
                        GitHub
                      </span>
                    </a>
                  </div>
                </div>
              </div>

              {/* Person 2 */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/5 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative bg-slate-900/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-8 hover:border-purple-500/30 transition-all duration-300">
                  {/* Avatar placeholder */}
                  <div className="w-20 h-20 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full flex items-center justify-center mb-6 mx-auto border border-purple-500/30">
                    <span className="text-2xl font-bold text-purple-400">
                      JS
                    </span>
                  </div>

                  <h3 className="text-2xl font-semibold text-white mb-2">
                    Jane Smith
                  </h3>
                  <p className="text-gray-400 mb-6 text-sm">
                    Frontend Developer & UI/UX Designer
                  </p>

                  {/* Social Links */}
                  <div className="flex justify-center space-x-4">
                    <a
                      href="https://linkedin.com/in/janesmith"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group/link flex items-center space-x-2 px-4 py-2 bg-slate-800/50 hover:bg-blue-600/20 border border-slate-600/50 hover:border-blue-500/50 rounded-lg transition-all duration-300"
                    >
                      <svg
                        className="w-5 h-5 text-blue-400 group-hover/link:text-blue-300"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                      </svg>
                      <span className="text-sm text-gray-300 group-hover/link:text-white">
                        LinkedIn
                      </span>
                    </a>

                    <a
                      href="https://github.com/janesmith"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group/link flex items-center space-x-2 px-4 py-2 bg-slate-800/50 hover:bg-slate-600/20 border border-slate-600/50 hover:border-slate-500/50 rounded-lg transition-all duration-300"
                    >
                      <svg
                        className="w-5 h-5 text-gray-400 group-hover/link:text-gray-300"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                      </svg>
                      <span className="text-sm text-gray-300 group-hover/link:text-white">
                        GitHub
                      </span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </ProfessionalHeroBackground>
  );
}
